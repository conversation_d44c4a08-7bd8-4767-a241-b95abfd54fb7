/*
 * Copyright (c) 2025 Bytedance, Inc. and its affiliates.
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Knowledge Base for Loop Detection and Resolution
 *
 * This module contains specialized knowledge for different types of loops
 * that can occur during agent execution, along with detection and resolution logic.
 */

import { SPECIAL_KNOWLEDGE_BASE, KnowledgeEntry, MessageHistoryEntry } from './knowledgeBase';

// ===== Loop Detection Functions =====

/**
 * Detects if a loop has been reported in the message history
 * @param messageHistory Array of message history entries
 * @returns true if loop is detected, false otherwise
 */
export function detectLoopInMessageHistory(messageHistory: MessageHistoryEntry[]): boolean {
  // 检查最新的assistant消息是否包含LOOP_DETECTED（LLM自检上报）
  const lastMessage = messageHistory[messageHistory.length - 1];
  if (lastMessage?.role === 'assistant' && typeof lastMessage.content === 'string') {
    const content = lastMessage.content.toLowerCase();
    return content.includes('loop_detected');
  }
  return false;
}

/**
 * Extracts the loop type from message history content
 * @param messageHistory Array of message history entries
 * @returns Loop type string (click, wait, drag, or empty string)
 */
export function extractLoopTypeFromHistory(messageHistory: MessageHistoryEntry[]): 'click' | 'wait' | 'drag' | '' {
  const lastMessage = messageHistory[messageHistory.length - 1];
  if (lastMessage?.role === 'assistant' && typeof lastMessage.content === 'string') {
    const content = lastMessage.content.toLowerCase();
    if (content.includes('click')) return 'click';
    if (content.includes('wait')) return 'wait';
    if (content.includes('drag')) return 'drag';
  }
  return '';
}

/**
 * Generates specialized knowledge based on detected loop type
 * @param messageHistory Optional array of message history entries
 * @returns Specialized knowledge string for the detected loop type
 */
export function generateLoopKnowledge(messageHistory?: MessageHistoryEntry[]): string {
  // 1. 没有历史，直接返回通用
  if (!messageHistory) {
    // 优先通用点击，其次日历
    return (
      SPECIAL_KNOWLEDGE_BASE.find((k) => k.id === 'general_click_loop')?.solution ||
      'Fallback solution here'
    );
  }

  // 2. 提取 loop 类型
  const loopType = extractLoopTypeFromHistory(messageHistory);

  // 3. 如果是 click 相关，拼接所有 click 相关 solution
  if (loopType.includes('click')) {
    return SPECIAL_KNOWLEDGE_BASE.filter((k) => k.category === 'click_loop')
      .map((k) => k.solution)
      .join('\n\n');
  }
  // 4. 如果是 wait 相关，拼接所有 wait 相关 solution
  if (loopType.includes('wait')) {
    return SPECIAL_KNOWLEDGE_BASE.filter((k) => k.category === 'wait_loop')
      .map((k) => k.solution)
      .join('\n\n');
  }

  // 5. 如果是 drag 相关，拼接所有 drag 相关 solution
  if (loopType.includes('drag')) {
    return SPECIAL_KNOWLEDGE_BASE.filter((k) => k.category === 'drag_loop')
      .map((k) => k.solution)
      .join('\n\n');
  }

  return '';
}
